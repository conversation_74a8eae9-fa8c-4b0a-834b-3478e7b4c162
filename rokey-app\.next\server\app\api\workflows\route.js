(()=>{var e={};e.id=8616,e.ids=[8616],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},38249:(e,t,r)=>{"use strict";r.d(t,{P:()=>u});var s=r(39398),a=r(55511),o=r.n(a);let i=(0,s.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}async saveWorkflow(e,t,r,s,a,n={}){try{let u=o().randomUUID(),d={id:u,user_id:e,name:t,description:r,nodes:s,edges:a,settings:n,is_active:!0,is_template:!1,version:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:l,error:c}=await i.from("manual_build_workflows").insert(d).select().single();if(c)throw Error(`Failed to save workflow: ${c.message}`);let w=await this.generateWorkflowAPIKey(u,e,t);return{workflow:l,apiKey:w}}catch(e){throw e}}async updateWorkflow(e,t,r){try{let{data:s}=await i.from("manual_build_workflows").select("version").eq("id",e).eq("user_id",t).single(),a={...r,updated_at:new Date().toISOString(),version:(s?.version||0)+1},{data:o,error:n}=await i.from("manual_build_workflows").update(a).eq("id",e).eq("user_id",t).select().single();if(n)throw Error(`Failed to update workflow: ${n.message}`);return o}catch(e){throw e}}async getUserWorkflows(e){try{let{data:t,error:r}=await i.from("manual_build_workflows").select("*").eq("user_id",e).eq("is_template",!1).order("updated_at",{ascending:!1});if(r)throw Error(`Failed to fetch workflows: ${r.message}`);return t}catch(e){throw e}}async getWorkflow(e,t){try{let{data:r,error:s}=await i.from("manual_build_workflows").select("*").eq("id",e).eq("user_id",t).single();if(s){if("PGRST116"===s.code)return null;throw Error(`Failed to fetch workflow: ${s.message}`)}return r}catch(e){throw e}}async deleteWorkflow(e,t){try{await i.from("workflow_api_keys").delete().eq("workflow_id",e).eq("user_id",t);let{error:r}=await i.from("manual_build_workflows").delete().eq("id",e).eq("user_id",t);if(r)throw Error(`Failed to delete workflow: ${r.message}`)}catch(e){throw e}}async generateWorkflowAPIKey(e,t,r){try{let s="rk_wf",a=o().randomBytes(32).toString("hex"),n=`${s}_${a}`,u=o().createHash("sha256").update(n).digest("hex"),d=a.slice(-8),l=o().createHash("md5").update(d).digest("hex"),c={id:o().randomUUID(),workflow_id:e,user_id:t,key_name:`${r} API Key`,key_prefix:s,key_hash:u,encrypted_key_suffix:l,permissions:{execute:!0,read_logs:!0,read_status:!0},status:"active",total_requests:0,created_at:new Date().toISOString()},{error:w}=await i.from("workflow_api_keys").insert(c);if(w)throw Error(`Failed to create API key: ${w.message}`);return n}catch(e){throw e}}async getWorkflowAPIKey(e,t){try{let{data:r,error:s}=await i.from("workflow_api_keys").select("*").eq("workflow_id",e).eq("user_id",t).single();if(s){if("PGRST116"===s.code)return null;throw Error(`Failed to fetch API key: ${s.message}`)}return r}catch(e){throw e}}async validateWorkflowAPIKey(e){try{let t=o().createHash("sha256").update(e).digest("hex"),{data:r,error:s}=await i.from("workflow_api_keys").select("workflow_id, user_id, status").eq("key_hash",t).eq("status","active").single();if(s||!r)return{valid:!1};return await i.from("workflow_api_keys").update({last_used_at:new Date().toISOString(),total_requests:i.raw("total_requests + 1")}).eq("key_hash",t),{valid:!0,workflowId:r.workflow_id,userId:r.user_id}}catch(e){return{valid:!1}}}async getWorkflowTemplates(e){try{let t=i.from("manual_build_workflows").select("*").eq("is_template",!0);e&&(t=t.eq("template_category",e));let{data:r,error:s}=await t.order("created_at",{ascending:!1});if(s)throw Error(`Failed to fetch templates: ${s.message}`);return r}catch(e){throw e}}}let u=n.getInstance()},39727:()=>{},44803:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>_,serverHooks:()=>y,workAsyncStorage:()=>k,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{DELETE:()=>p,GET:()=>c,POST:()=>w,PUT:()=>f});var a=r(96559),o=r(48088),i=r(37719),n=r(32190),u=r(61223),d=r(44999),l=r(38249);async function c(e){try{let t=(0,u.createRouteHandlerClient)({cookies:d.UL}),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),o=a.get("id");if(o){let e=await l.P.getWorkflow(o,r.id);if(!e)return n.NextResponse.json({error:"Workflow not found"},{status:404});let t=await l.P.getWorkflowAPIKey(o,r.id);return n.NextResponse.json({workflow:e,api_key_info:t?{id:t.id,key_name:t.key_name,key_prefix:t.key_prefix,encrypted_suffix:t.encrypted_key_suffix,status:t.status,total_requests:t.total_requests,last_used_at:t.last_used_at,created_at:t.created_at}:null})}{let e=await l.P.getUserWorkflows(r.id);return n.NextResponse.json({workflows:e.map(e=>({id:e.id,name:e.name,description:e.description,is_active:e.is_active,version:e.version,created_at:e.created_at,updated_at:e.updated_at,node_count:e.nodes.length,edge_count:e.edges.length}))})}}catch(e){return n.NextResponse.json({error:"Failed to fetch workflows",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function w(e){try{let t=(0,u.createRouteHandlerClient)({cookies:d.UL}),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{name:a,description:o,nodes:i,edges:c,settings:w}=await e.json();if(!a||!i||!c)return n.NextResponse.json({error:"Missing required fields: name, nodes, edges"},{status:400});if(!Array.isArray(i)||0===i.length)return n.NextResponse.json({error:"Nodes must be a non-empty array"},{status:400});if(!i.some(e=>"userRequest"===e.type))return n.NextResponse.json({error:"Workflow must contain at least one User Request node"},{status:400});let{workflow:f,apiKey:p}=await l.P.saveWorkflow(r.id,a,o||"",i,c,w||{});return n.NextResponse.json({success:!0,workflow:{id:f.id,name:f.name,description:f.description,is_active:f.is_active,version:f.version,created_at:f.created_at,updated_at:f.updated_at},api_key:p,message:"Workflow saved successfully. Save your API key - it will not be shown again!"})}catch(e){return n.NextResponse.json({error:"Failed to save workflow",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function f(e){try{let t=(0,u.createRouteHandlerClient)({cookies:d.UL}),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{id:a,name:o,description:i,nodes:c,edges:w,settings:f,is_active:p}=await e.json();if(!a)return n.NextResponse.json({error:"Workflow ID is required"},{status:400});let _=await l.P.updateWorkflow(a,r.id,{name:o,description:i,nodes:c,edges:w,settings:f,is_active:p});return n.NextResponse.json({success:!0,workflow:{id:_.id,name:_.name,description:_.description,is_active:_.is_active,version:_.version,updated_at:_.updated_at}})}catch(e){return n.NextResponse.json({error:"Failed to update workflow",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e){try{let t=(0,u.createRouteHandlerClient)({cookies:d.UL}),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),o=a.get("id");if(!o)return n.NextResponse.json({error:"Workflow ID is required"},{status:400});return await l.P.deleteWorkflow(o,r.id),n.NextResponse.json({success:!0,message:"Workflow deleted successfully"})}catch(e){return n.NextResponse.json({error:"Failed to delete workflow",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let _=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/route",pathname:"/api/workflows",filename:"route",bundlePath:"app/api/workflows/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\workflows\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:k,workUnitAsyncStorage:h,serverHooks:y}=_;function m(){return(0,i.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:h})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,4999,1223],()=>r(44803));module.exports=s})();