/**
 * Workflow Persistence Service
 * Handles saving, loading, and managing workflows similar to how models work
 */

import { createClient } from '@supabase/supabase-js';
import { WorkflowNode } from '@/types/manualBuild';
import crypto from 'crypto';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface SavedWorkflow {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  nodes: WorkflowNode[];
  edges: any[];
  settings: any;
  custom_api_config_id?: string;
  is_active: boolean;
  is_template: boolean;
  template_category?: string;
  version: number;
  created_at: string;
  updated_at: string;
  api_key?: string; // Persistent API key for this workflow
}

export interface WorkflowAPIKey {
  id: string;
  workflow_id: string;
  user_id: string;
  key_name: string;
  key_prefix: string;
  key_hash: string;
  encrypted_key_suffix: string;
  permissions: any;
  status: 'active' | 'inactive';
  total_requests: number;
  last_used_at?: string;
  created_at: string;
}

export class WorkflowPersistenceService {
  private static instance: WorkflowPersistenceService;

  static getInstance(): WorkflowPersistenceService {
    if (!WorkflowPersistenceService.instance) {
      WorkflowPersistenceService.instance = new WorkflowPersistenceService();
    }
    return WorkflowPersistenceService.instance;
  }

  /**
   * Save a new workflow (similar to saving models)
   */
  async saveWorkflow(
    userId: string,
    name: string,
    description: string,
    nodes: WorkflowNode[],
    edges: any[],
    settings: any = {}
  ): Promise<{ workflow: SavedWorkflow; apiKey: string }> {
    try {
      // Create workflow record
      const workflowId = crypto.randomUUID();
      const workflowData = {
        id: workflowId,
        user_id: userId,
        name,
        description,
        nodes,
        edges,
        settings,
        is_active: true,
        is_template: false,
        version: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: workflow, error: workflowError } = await supabase
        .from('manual_build_workflows')
        .insert(workflowData)
        .select()
        .single();

      if (workflowError) {
        throw new Error(`Failed to save workflow: ${workflowError.message}`);
      }

      // Generate persistent API key for this workflow
      const apiKey = await this.generateWorkflowAPIKey(workflowId, userId, name);

      return {
        workflow: workflow as SavedWorkflow,
        apiKey
      };
    } catch (error) {
      console.error('Error saving workflow:', error);
      throw error;
    }
  }

  /**
   * Update an existing workflow
   */
  async updateWorkflow(
    workflowId: string,
    userId: string,
    updates: Partial<{
      name: string;
      description: string;
      nodes: WorkflowNode[];
      edges: any[];
      settings: any;
      is_active: boolean;
    }>
  ): Promise<SavedWorkflow> {
    try {
      // First get the current version
      const { data: currentWorkflow } = await supabase
        .from('manual_build_workflows')
        .select('version')
        .eq('id', workflowId)
        .eq('user_id', userId)
        .single();

      const updateData = {
        ...updates,
        updated_at: new Date().toISOString(),
        version: (currentWorkflow?.version || 0) + 1 // Increment version
      };

      const { data: workflow, error } = await supabase
        .from('manual_build_workflows')
        .update(updateData)
        .eq('id', workflowId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update workflow: ${error.message}`);
      }

      return workflow as SavedWorkflow;
    } catch (error) {
      console.error('Error updating workflow:', error);
      throw error;
    }
  }

  /**
   * Get user's workflows
   */
  async getUserWorkflows(userId: string): Promise<SavedWorkflow[]> {
    try {
      const { data: workflows, error } = await supabase
        .from('manual_build_workflows')
        .select('*')
        .eq('user_id', userId)
        .eq('is_template', false)
        .order('updated_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch workflows: ${error.message}`);
      }

      return workflows as SavedWorkflow[];
    } catch (error) {
      console.error('Error fetching workflows:', error);
      throw error;
    }
  }

  /**
   * Get a specific workflow
   */
  async getWorkflow(workflowId: string, userId: string): Promise<SavedWorkflow | null> {
    try {
      const { data: workflow, error } = await supabase
        .from('manual_build_workflows')
        .select('*')
        .eq('id', workflowId)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        throw new Error(`Failed to fetch workflow: ${error.message}`);
      }

      return workflow as SavedWorkflow;
    } catch (error) {
      console.error('Error fetching workflow:', error);
      throw error;
    }
  }

  /**
   * Delete a workflow and its API key
   */
  async deleteWorkflow(workflowId: string, userId: string): Promise<void> {
    try {
      // Delete workflow API key first
      await supabase
        .from('workflow_api_keys')
        .delete()
        .eq('workflow_id', workflowId)
        .eq('user_id', userId);

      // Delete workflow
      const { error } = await supabase
        .from('manual_build_workflows')
        .delete()
        .eq('id', workflowId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(`Failed to delete workflow: ${error.message}`);
      }
    } catch (error) {
      console.error('Error deleting workflow:', error);
      throw error;
    }
  }

  /**
   * Generate persistent API key for workflow
   */
  private async generateWorkflowAPIKey(
    workflowId: string,
    userId: string,
    workflowName: string
  ): Promise<string> {
    try {
      // Generate API key similar to your existing system
      const keyPrefix = 'rk_wf'; // RouKey Workflow
      const keyBody = crypto.randomBytes(32).toString('hex');
      const fullKey = `${keyPrefix}_${keyBody}`;
      
      // Hash the key for storage
      const keyHash = crypto.createHash('sha256').update(fullKey).digest('hex');
      
      // Encrypt the suffix for partial display
      const keySuffix = keyBody.slice(-8);
      const encryptedSuffix = crypto.createHash('md5').update(keySuffix).digest('hex');

      // Store API key
      const apiKeyData = {
        id: crypto.randomUUID(),
        workflow_id: workflowId,
        user_id: userId,
        key_name: `${workflowName} API Key`,
        key_prefix: keyPrefix,
        key_hash: keyHash,
        encrypted_key_suffix: encryptedSuffix,
        permissions: {
          execute: true,
          read_logs: true,
          read_status: true
        },
        status: 'active',
        total_requests: 0,
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('workflow_api_keys')
        .insert(apiKeyData);

      if (error) {
        throw new Error(`Failed to create API key: ${error.message}`);
      }

      return fullKey;
    } catch (error) {
      console.error('Error generating workflow API key:', error);
      throw error;
    }
  }

  /**
   * Get workflow API key info (without the actual key)
   */
  async getWorkflowAPIKey(workflowId: string, userId: string): Promise<WorkflowAPIKey | null> {
    try {
      const { data: apiKey, error } = await supabase
        .from('workflow_api_keys')
        .select('*')
        .eq('workflow_id', workflowId)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        throw new Error(`Failed to fetch API key: ${error.message}`);
      }

      return apiKey as WorkflowAPIKey;
    } catch (error) {
      console.error('Error fetching workflow API key:', error);
      throw error;
    }
  }

  /**
   * Validate workflow API key for execution
   */
  async validateWorkflowAPIKey(apiKey: string): Promise<{ valid: boolean; workflowId?: string; userId?: string }> {
    try {
      const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

      const { data: keyRecord, error } = await supabase
        .from('workflow_api_keys')
        .select('workflow_id, user_id, status')
        .eq('key_hash', keyHash)
        .eq('status', 'active')
        .single();

      if (error || !keyRecord) {
        return { valid: false };
      }

      // Update last used timestamp
      await supabase
        .from('workflow_api_keys')
        .update({
          last_used_at: new Date().toISOString(),
          total_requests: supabase.raw('total_requests + 1')
        })
        .eq('key_hash', keyHash);

      return {
        valid: true,
        workflowId: keyRecord.workflow_id,
        userId: keyRecord.user_id
      };
    } catch (error) {
      console.error('Error validating workflow API key:', error);
      return { valid: false };
    }
  }

  /**
   * Get workflow templates
   */
  async getWorkflowTemplates(category?: string): Promise<SavedWorkflow[]> {
    try {
      let query = supabase
        .from('manual_build_workflows')
        .select('*')
        .eq('is_template', true);

      if (category) {
        query = query.eq('template_category', category);
      }

      const { data: templates, error } = await query.order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch templates: ${error.message}`);
      }

      return templates as SavedWorkflow[];
    } catch (error) {
      console.error('Error fetching workflow templates:', error);
      throw error;
    }
  }
}

export const workflowPersistence = WorkflowPersistenceService.getInstance();
