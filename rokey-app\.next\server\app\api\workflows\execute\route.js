"use strict";(()=>{var e={};e.id=4016,e.ids=[4016],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},38249:(e,t,r)=>{r.d(t,{P:()=>d});var o=r(39398),a=r(55511),s=r.n(a);let i=(0,o.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}async saveWorkflow(e,t,r,o,a,n={}){try{let d=s().randomUUID(),u={id:d,user_id:e,name:t,description:r,nodes:o,edges:a,settings:n,is_active:!0,is_template:!1,version:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:l,error:w}=await i.from("manual_build_workflows").insert(u).select().single();if(w)throw Error(`Failed to save workflow: ${w.message}`);let c=await this.generateWorkflowAPIKey(d,e,t);return{workflow:l,apiKey:c}}catch(e){throw e}}async updateWorkflow(e,t,r){try{let{data:o}=await i.from("manual_build_workflows").select("version").eq("id",e).eq("user_id",t).single(),a={...r,updated_at:new Date().toISOString(),version:(o?.version||0)+1},{data:s,error:n}=await i.from("manual_build_workflows").update(a).eq("id",e).eq("user_id",t).select().single();if(n)throw Error(`Failed to update workflow: ${n.message}`);return s}catch(e){throw e}}async getUserWorkflows(e){try{let{data:t,error:r}=await i.from("manual_build_workflows").select("*").eq("user_id",e).eq("is_template",!1).order("updated_at",{ascending:!1});if(r)throw Error(`Failed to fetch workflows: ${r.message}`);return t}catch(e){throw e}}async getWorkflow(e,t){try{let{data:r,error:o}=await i.from("manual_build_workflows").select("*").eq("id",e).eq("user_id",t).single();if(o){if("PGRST116"===o.code)return null;throw Error(`Failed to fetch workflow: ${o.message}`)}return r}catch(e){throw e}}async deleteWorkflow(e,t){try{await i.from("workflow_api_keys").delete().eq("workflow_id",e).eq("user_id",t);let{error:r}=await i.from("manual_build_workflows").delete().eq("id",e).eq("user_id",t);if(r)throw Error(`Failed to delete workflow: ${r.message}`)}catch(e){throw e}}async generateWorkflowAPIKey(e,t,r){try{let o="rk_wf",a=s().randomBytes(32).toString("hex"),n=`${o}_${a}`,d=s().createHash("sha256").update(n).digest("hex"),u=a.slice(-8),l=s().createHash("md5").update(u).digest("hex"),w={id:s().randomUUID(),workflow_id:e,user_id:t,key_name:`${r} API Key`,key_prefix:o,key_hash:d,encrypted_key_suffix:l,permissions:{execute:!0,read_logs:!0,read_status:!0},status:"active",total_requests:0,created_at:new Date().toISOString()},{error:c}=await i.from("workflow_api_keys").insert(w);if(c)throw Error(`Failed to create API key: ${c.message}`);return n}catch(e){throw e}}async getWorkflowAPIKey(e,t){try{let{data:r,error:o}=await i.from("workflow_api_keys").select("*").eq("workflow_id",e).eq("user_id",t).single();if(o){if("PGRST116"===o.code)return null;throw Error(`Failed to fetch API key: ${o.message}`)}return r}catch(e){throw e}}async validateWorkflowAPIKey(e){try{let t=s().createHash("sha256").update(e).digest("hex"),{data:r,error:o}=await i.from("workflow_api_keys").select("workflow_id, user_id, status").eq("key_hash",t).eq("status","active").single();if(o||!r)return{valid:!1};return await i.from("workflow_api_keys").update({last_used_at:new Date().toISOString(),total_requests:i.raw("total_requests + 1")}).eq("key_hash",t),{valid:!0,workflowId:r.workflow_id,userId:r.user_id}}catch(e){return{valid:!1}}}async getWorkflowTemplates(e){try{let t=i.from("manual_build_workflows").select("*").eq("is_template",!0);e&&(t=t.eq("template_category",e));let{data:r,error:o}=await t.order("created_at",{ascending:!1});if(o)throw Error(`Failed to fetch templates: ${o.message}`);return r}catch(e){throw e}}}let d=n.getInstance()},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},82279:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>_,serverHooks:()=>m,workAsyncStorage:()=>k,workUnitAsyncStorage:()=>x});var o={};r.r(o),r.d(o,{GET:()=>f,POST:()=>p});var a=r(96559),s=r(48088),i=r(37719),n=r(32190),d=r(38249),u=r(41489),l=r(62480),w=r(55511),c=r.n(w);async function p(e){try{let t=e.headers.get("X-API-Key");if(!t)return n.NextResponse.json({error:"API key required in X-API-Key header"},{status:401});let r=await d.P.validateWorkflowAPIKey(t);if(!r.valid||!r.workflowId||!r.userId)return n.NextResponse.json({error:"Invalid or expired API key"},{status:401});let{input:o,options:a={}}=await e.json();if(!o)return n.NextResponse.json({error:"Input is required"},{status:400});let s=await d.P.getWorkflow(r.workflowId,r.userId);if(!s||!s.is_active)return n.NextResponse.json({error:"Workflow not found or inactive"},{status:404});let i=c().randomUUID();await l.a.startExecution(i,s.id,r.userId,s.nodes.length);try{let e=u.J.getInstance(),t=await e.executeWorkflow(s.id,r.userId,s.nodes,s.edges,o);return await l.a.completeExecution(i,t,Date.now()),n.NextResponse.json({success:!0,execution_id:i,workflow_id:s.id,workflow_name:s.name,result:t,executed_at:new Date().toISOString(),execution_time_ms:Date.now()})}catch(e){throw await l.a.failExecution(i,e instanceof Error?e.message:"Unknown execution error",{error:e}),e}}catch(e){return n.NextResponse.json({error:"Workflow execution failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function f(e){try{let t=e.headers.get("X-API-Key");if(!t)return n.NextResponse.json({error:"API key required in X-API-Key header"},{status:401});let r=await d.P.validateWorkflowAPIKey(t);if(!r.valid||!r.workflowId||!r.userId)return n.NextResponse.json({error:"Invalid or expired API key"},{status:401});let{searchParams:o}=new URL(e.url),a=o.get("execution_id");if(a){let e=l.a.getExecutionStatus(a);if(!e)return n.NextResponse.json({error:"Execution not found"},{status:404});return n.NextResponse.json({execution_id:a,status:e.status,progress:e.progress,current_node:e.currentNodeId,logs:e.logs,result:e.result,error:e.error,timestamp:e.timestamp})}{let e=await d.P.getWorkflow(r.workflowId,r.userId);if(!e)return n.NextResponse.json({error:"Workflow not found"},{status:404});let t=await l.a.getExecutionHistory(e.id,r.userId,10);return n.NextResponse.json({workflow:{id:e.id,name:e.name,description:e.description,is_active:e.is_active,version:e.version,created_at:e.created_at,updated_at:e.updated_at},recent_executions:t.map(e=>({id:e.id,status:e.status,started_at:e.started_at,completed_at:e.completed_at,execution_time_ms:e.execution_time_ms,nodes_executed:e.nodes_executed,nodes_total:e.nodes_total}))})}}catch(e){return n.NextResponse.json({error:"Failed to get workflow status",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let _=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/workflows/execute/route",pathname:"/api/workflows/execute",filename:"route",bundlePath:"app/api/workflows/execute/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\workflows\\execute\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:k,workUnitAsyncStorage:x,serverHooks:m}=_;function h(){return(0,i.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:x})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,580,9398,4234,5411],()=>r(82279));module.exports=o})();