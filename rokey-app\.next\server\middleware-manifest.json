{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "XsMW0nuIhu3EBrXSRgg-5", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "188941c61d12db9149580a8068c70148", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fea6bd2d5bfde28ab5105de636eeb5d8c316e46defa05f535af9ad69324ba513", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "13ea9b825c4cc61fc618c65fe55e496e88c6e78cccdb7d522d5b22e1480deef9"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "XsMW0nuIhu3EBrXSRgg-5", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "188941c61d12db9149580a8068c70148", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fea6bd2d5bfde28ab5105de636eeb5d8c316e46defa05f535af9ad69324ba513", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "13ea9b825c4cc61fc618c65fe55e496e88c6e78cccdb7d522d5b22e1480deef9"}}}, "sortedMiddleware": ["/"]}